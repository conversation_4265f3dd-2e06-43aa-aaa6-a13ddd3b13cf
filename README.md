# موقع مطعم وكافيه روما - Roma Restaurant & Cafe Website

موقع ويب حديث ومتجاوب لمطعم وكافيه روما، مصمم باستخدام HTML و CSS و JavaScript العادي بدون مكتبات خارجية.

## المميزات

### 🎨 التصميم
- تصميم متجاوب يعمل على جميع الأجهزة
- ألوان متناسقة مع هوية روما (ذهبي، أسود، أبيض)
- تأثيرات بصرية جذابة وسلسة
- خط Cairo العربي الجميل

### 📱 الوظائف
- قائمة تنقل ثابتة مع تأثير الشفافية
- قائمة هامبرغر للأجهزة المحمولة
- تبديل بين قوائم المطعم والكافيه
- معرض صور تفاعلي مع lightbox
- نموذج اتصال مع التحقق من البيانات
- تأثيرات التمرير والحركة

### 🍽️ المحتوى
- قسم البطل مع عنوان متحرك
- معلومات عن المطعم والكافيه
- قوائم الطعام والمشروبات
- معرض صور للأجواء
- معلومات الاتصال والموقع

## هيكل الملفات

```
roma-website/
├── index.html          # الصفحة الرئيسية
├── styles.css          # ملف التصميم
├── script.js           # ملف JavaScript
└── README.md           # هذا الملف
```

## كيفية الاستخدام

1. **فتح الموقع**: افتح ملف `index.html` في أي متصفح ويب
2. **إضافة الصور**: أضف الصور التالية في نفس المجلد:
   - `logo.png` - شعار روما
   - `hero-image.jpg` - صورة القسم الرئيسي
   - `about-image.jpg` - صورة قسم "عن روما"
   - `pasta.jpg`, `pizza.jpg`, `risotto.jpg` - صور أطباق المطعم
   - `espresso.jpg`, `cappuccino.jpg`, `juice.jpg` - صور مشروبات الكافيه
   - `gallery1.jpg` إلى `gallery4.jpg` - صور المعرض

## التخصيص

### تغيير الألوان
في ملف `styles.css`، يمكنك تغيير الألوان الأساسية:
```css
/* اللون الذهبي الرئيسي */
#D4AF37

/* اللون الأسود */
#333

/* اللون الرمادي */
#666
```

### إضافة عناصر جديدة للقائمة
في ملف `index.html`، أضف عناصر جديدة داخل `.menu-grid`:
```html
<div class="menu-item">
    <img src="طبق-جديد.jpg" alt="اسم الطبق">
    <h3>اسم الطبق</h3>
    <p>وصف الطبق</p>
    <span class="price">السعر</span>
</div>
```

### تعديل معلومات الاتصال
في قسم `#contact`، قم بتحديث:
- العنوان
- رقم الهاتف
- ساعات العمل
- روابط وسائل التواصل الاجتماعي

## المتطلبات

- متصفح ويب حديث يدعم:
  - CSS Grid و Flexbox
  - JavaScript ES6+
  - CSS Transitions و Animations

## الدعم

الموقع متوافق مع:
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله حسب الحاجة.

---

**ملاحظة**: تأكد من إضافة الصور المطلوبة لضمان عمل الموقع بشكل صحيح.
