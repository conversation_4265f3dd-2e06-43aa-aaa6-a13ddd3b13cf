// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Mobile menu toggle
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-menu a').forEach(link => {
    link.addEventListener('click', () => {
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
    });
});

// Header background on scroll
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(0, 0, 0, 0.95)';
    } else {
        header.style.background = 'rgba(0, 0, 0, 0.9)';
    }
});

// Menu tabs functionality
const tabButtons = document.querySelectorAll('.tab-btn');
const menuContents = document.querySelectorAll('.menu-content');

tabButtons.forEach(button => {
    button.addEventListener('click', () => {
        // Remove active class from all buttons and contents
        tabButtons.forEach(btn => btn.classList.remove('active'));
        menuContents.forEach(content => content.classList.remove('active'));
        
        // Add active class to clicked button
        button.classList.add('active');
        
        // Show corresponding content
        const targetTab = button.getAttribute('data-tab');
        document.getElementById(targetTab).classList.add('active');
    });
});

// Contact form handling
const contactForm = document.querySelector('.contact-form form');
contactForm.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Get form data
    const name = this.querySelector('input[type="text"]').value;
    const email = this.querySelector('input[type="email"]').value;
    const phone = this.querySelector('input[type="tel"]').value;
    const message = this.querySelector('textarea').value;
    
    // Simple validation
    if (!name || !email || !phone || !message) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // Simulate form submission
    alert('شكراً لك! تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.');
    this.reset();
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', () => {
    const animatedElements = document.querySelectorAll('.menu-item, .gallery-item, .feature, .contact-item');
    
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Gallery lightbox effect (simple implementation)
const galleryItems = document.querySelectorAll('.gallery-item img');
galleryItems.forEach(img => {
    img.addEventListener('click', () => {
        // Create lightbox overlay
        const lightbox = document.createElement('div');
        lightbox.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            cursor: pointer;
        `;
        
        const lightboxImg = document.createElement('img');
        lightboxImg.src = img.src;
        lightboxImg.style.cssText = `
            max-width: 90%;
            max-height: 90%;
            border-radius: 10px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        `;
        
        lightbox.appendChild(lightboxImg);
        document.body.appendChild(lightbox);
        
        // Close lightbox on click
        lightbox.addEventListener('click', () => {
            document.body.removeChild(lightbox);
        });
        
        // Close lightbox on escape key
        document.addEventListener('keydown', function escapeHandler(e) {
            if (e.key === 'Escape') {
                if (document.body.contains(lightbox)) {
                    document.body.removeChild(lightbox);
                }
                document.removeEventListener('keydown', escapeHandler);
            }
        });
    });
});

// Add loading animation
window.addEventListener('load', () => {
    document.body.style.opacity = '1';
});

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    // Add some interactive hover effects
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.addEventListener('mouseenter', () => {
            item.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        item.addEventListener('mouseleave', () => {
            item.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Add parallax effect to hero section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const heroImage = document.querySelector('.hero-image img');
        if (heroImage) {
            heroImage.style.transform = `translateY(${scrolled * 0.3}px)`;
        }
    });
});

// Add typing effect to hero title
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// Initialize typing effect when page loads
window.addEventListener('load', () => {
    const titleLine2 = document.querySelector('.title-line-2');
    if (titleLine2) {
        const originalText = titleLine2.textContent;
        setTimeout(() => {
            typeWriter(titleLine2, originalText, 150);
        }, 800);
    }
});

// Hero scroll indicator click
const scrollIndicator = document.querySelector('.hero-scroll-indicator');
if (scrollIndicator) {
    scrollIndicator.addEventListener('click', () => {
        document.querySelector('#about').scrollIntoView({
            behavior: 'smooth'
        });
    });
}

// Play button functionality
const playButton = document.querySelector('.play-button');
if (playButton) {
    playButton.addEventListener('click', () => {
        // Here you can add video modal or redirect to video
        alert('سيتم إضافة فيديو تعريفي قريباً!');
    });
}

// Animate stats numbers on scroll
const animateStats = () => {
    const statNumbers = document.querySelectorAll('.stat-number');

    statNumbers.forEach(stat => {
        const target = parseInt(stat.textContent);
        const increment = target / 50;
        let current = 0;

        const updateStat = () => {
            if (current < target) {
                current += increment;
                stat.textContent = Math.ceil(current) + '+';
                requestAnimationFrame(updateStat);
            } else {
                stat.textContent = target + '+';
            }
        };

        updateStat();
    });
};

// Trigger stats animation when hero section is visible
const heroObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            animateStats();
            heroObserver.unobserve(entry.target);
        }
    });
}, { threshold: 0.5 });

const heroSection = document.querySelector('.hero');
if (heroSection) {
    heroObserver.observe(heroSection);
}

// Enhanced parallax effect for floating elements
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const rate = scrolled * -0.5;

    // Parallax for floating elements
    const floatingElements = document.querySelectorAll('.floating-element');
    floatingElements.forEach((element, index) => {
        const speed = 0.3 + (index * 0.1);
        element.style.transform = `translateY(${scrolled * speed}px) rotate(${scrolled * 0.02}deg)`;
    });

    // Parallax for hero image
    const heroImage = document.querySelector('.hero-main-image');
    if (heroImage) {
        heroImage.style.transform = `translateY(${rate}px) scale(${1 + scrolled * 0.0002})`;
    }
});

// Add mouse interaction with hero image to affect floating cards
const heroImageContainer = document.querySelector('.hero-image-container');
const heroFloatingCards = document.querySelectorAll('.floating-card');

if (heroImageContainer && heroFloatingCards.length > 0) {
    heroImageContainer.addEventListener('mousemove', (e) => {
        const rect = heroImageContainer.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const mouseX = e.clientX - centerX;
        const mouseY = e.clientY - centerY;

        // Calculate movement intensity based on distance from center
        const maxDistance = Math.sqrt(rect.width * rect.width + rect.height * rect.height) / 2;
        const distance = Math.sqrt(mouseX * mouseX + mouseY * mouseY);
        const intensity = Math.min(distance / maxDistance, 1);

        heroFloatingCards.forEach((card) => {
            const cardRect = card.getBoundingClientRect();
            const cardCenterX = cardRect.left + cardRect.width / 2;
            const cardCenterY = cardRect.top + cardRect.height / 2;

            // Calculate direction from mouse to card
            const directionX = (cardCenterX - e.clientX) / 1000;
            const directionY = (cardCenterY - e.clientY) / 1000;

            // Apply subtle movement
            const moveX = directionX * intensity * 20;
            const moveY = directionY * intensity * 20;
            const rotation = (mouseX / rect.width) * 5;

            card.style.transform += ` translate(${moveX}px, ${moveY}px) rotate(${rotation}deg)`;
        });
    });

    heroImageContainer.addEventListener('mouseleave', () => {
        // Reset cards to original positions
        heroFloatingCards.forEach((card, index) => {
            const originalTransforms = {
                0: 'rotate(-12deg) scale(0.85)',
                1: 'rotate(15deg) scale(0.8)',
                2: 'rotate(-18deg) scale(0.75)'
            };

            card.style.transition = 'transform 0.5s ease';
            card.style.transform = originalTransforms[index];

            setTimeout(() => {
                card.style.transition = '';
            }, 500);
        });
    });
}

// Enhanced floating cards interactions
const floatingCards = document.querySelectorAll('.floating-card');
floatingCards.forEach((card, index) => {
    // Add pointer events back for interaction
    card.style.pointerEvents = 'auto';
    card.style.cursor = 'pointer';

    // Store original transform values
    const originalTransforms = {
        1: 'rotate(-8deg) scale(0.9)',
        2: 'rotate(12deg) scale(0.85)',
        3: 'rotate(-15deg) scale(0.8)'
    };

    const cardNumber = index + 1;
    const originalTransform = originalTransforms[cardNumber];

    card.addEventListener('mouseenter', () => {
        card.style.transition = 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
        card.style.transform = `translateY(-20px) scale(1.1) rotate(0deg)`;
        card.style.boxShadow = `
            0 30px 60px rgba(0, 0, 0, 0.25),
            0 15px 30px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(212, 175, 55, 0.5)
        `;
        card.style.zIndex = '20';

        // Add glow effect
        card.style.filter = 'drop-shadow(0 0 20px rgba(212, 175, 55, 0.4))';
    });

    card.addEventListener('mouseleave', () => {
        card.style.transition = 'all 0.3s ease';
        card.style.transform = originalTransform;
        card.style.boxShadow = `
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 8px 16px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8)
        `;
        card.style.zIndex = '10';
        card.style.filter = 'none';
    });

    // Add click interaction
    card.addEventListener('click', () => {
        // Add pulse effect
        card.style.animation = 'none';
        card.style.transform = `${originalTransform} scale(0.95)`;

        setTimeout(() => {
            card.style.transform = originalTransform;
            card.style.animation = `cardFloat 4s ease-in-out infinite`;
            card.style.animationDelay = `${index * 1.5}s`;
        }, 150);

        // Show notification based on card type
        const cardTitle = card.querySelector('.card-title').textContent;
        showCardNotification(cardTitle);
    });
});

// Show notification for card interactions
function showCardNotification(cardTitle) {
    const notifications = {
        'تقييم ممتاز': 'شكراً لثقتكم! نحن نسعى دائماً لتقديم أفضل خدمة',
        'طبق اليوم': 'بيتزا روما الخاصة متوفرة الآن! اطلبها من القائمة',
        'قهوة طازجة': 'قهوتنا محمصة يومياً من أجود أنواع البن'
    };

    const message = notifications[cardTitle] || 'مرحباً بكم في روما!';

    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: linear-gradient(45deg, #D4AF37, #F4D03F);
        color: #1a1a1a;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        font-weight: 600;
        max-width: 300px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
