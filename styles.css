/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(0, 0, 0, 0.9);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-img {
    width: 40px;
    height: 40px;
}

.logo-text {
    font-size: 1.8rem;
    font-weight: 700;
    color: #D4AF37;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #D4AF37;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    padding: 100px 20px 50px;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    margin-right: 50px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: #D4AF37;
    color: white;
}

.btn-primary:hover {
    background: #B8941F;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #333;
    border: 2px solid #333;
}

.btn-secondary:hover {
    background: #333;
    color: white;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
}

/* About Section */
.about {
    padding: 80px 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h3 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 1rem;
}

.about-text p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.features {
    display: grid;
    gap: 1.5rem;
}

.feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.feature i {
    font-size: 2rem;
    color: #D4AF37;
    margin-top: 0.5rem;
}

.feature h4 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.feature p {
    color: #666;
    font-size: 1rem;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Menu Section */
.menu {
    padding: 80px 0;
    background: #f8f8f8;
}

.menu-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.tab-btn {
    padding: 12px 30px;
    border: 2px solid #D4AF37;
    background: transparent;
    color: #D4AF37;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: #D4AF37;
    color: white;
}

.menu-content {
    display: none;
}

.menu-content.active {
    display: block;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.menu-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.menu-item:hover {
    transform: translateY(-5px);
}

.menu-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.menu-item h3 {
    font-size: 1.3rem;
    color: #333;
    margin: 1rem;
}

.menu-item p {
    color: #666;
    margin: 0 1rem;
    font-size: 0.95rem;
}

.menu-item .price {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: #D4AF37;
    margin: 1rem;
    text-align: left;
}

/* Gallery Section */
.gallery {
    padding: 80px 0;
    background: white;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.gallery-item {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: #f8f8f8;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-item i {
    font-size: 1.5rem;
    color: #D4AF37;
    margin-top: 0.5rem;
}

.contact-item h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: #666;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 12px;
    margin-bottom: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #D4AF37;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 2rem 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-logo img {
    width: 30px;
    height: 30px;
}

.footer-logo span {
    font-size: 1.5rem;
    font-weight: 700;
    color: #D4AF37;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: white;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #D4AF37;
}

.footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #555;
}

/* Mobile Navigation Styles */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(0, 0, 0, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 120px 20px 50px;
    }

    .hero-content {
        margin-right: 0;
        margin-bottom: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .menu-grid {
        grid-template-columns: 1fr;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .menu-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
    }
}

/* Additional animations and effects */
.menu-item,
.gallery-item,
.feature,
.contact-item {
    transition: all 0.3s ease;
}

/* Scroll animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    animation: fadeInUp 0.6s ease forwards;
}

/* Loading spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #D4AF37;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hover effects for better UX */
.nav-menu a,
.btn,
.social-links a {
    position: relative;
    overflow: hidden;
}

.nav-menu a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: #D4AF37;
    transition: width 0.3s ease;
}

.nav-menu a:hover::before {
    width: 100%;
}

/* Improved form styling */
.contact-form input:focus,
.contact-form textarea:focus {
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

/* Better button hover effects */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}
