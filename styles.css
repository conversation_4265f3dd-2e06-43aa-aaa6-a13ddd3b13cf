/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(0, 0, 0, 0.9);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-img {
    width: 40px;
    height: 40px;
}

.logo-text {
    font-size: 1.8rem;
    font-weight: 700;
    color: #D4AF37;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #D4AF37;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.05) 0%, transparent 50%);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.3) 0%, rgba(212, 175, 55, 0.1) 100%);
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    font-size: 2rem;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.pasta-icon {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.coffee-icon {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.pizza-icon {
    top: 30%;
    right: 25%;
    animation-delay: 4s;
}

.leaf-icon {
    bottom: 30%;
    left: 20%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

.hero-container {
    display: flex;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 100px 20px 50px;
    gap: 4rem;
    position: relative;
    z-index: 2;
}

.hero-content {
    flex: 1;
    max-width: 650px;
}

.hero-badge {
    display: inline-block;
    background: linear-gradient(45deg, #D4AF37, #F4D03F);
    color: #1a1a1a;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.hero-title {
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.title-line-1 {
    display: block;
    font-size: 1.8rem;
    font-weight: 400;
    color: #D4AF37;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.title-line-2 {
    display: block;
    font-size: 4.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 0.3rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.title-accent {
    display: block;
    font-size: 2rem;
    font-weight: 300;
    color: #D4AF37;
    letter-spacing: 8px;
    opacity: 0.8;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #cccccc;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.subtitle-highlight {
    color: #D4AF37;
    font-weight: 600;
    font-style: italic;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
    padding: 1.5rem 0;
    border-top: 1px solid rgba(212, 175, 55, 0.2);
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #D4AF37;
    margin-bottom: 0.3rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #999;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #D4AF37, #F4D03F);
    color: #1a1a1a;
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(212, 175, 55, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
}

.btn-outline {
    background: transparent;
    color: #D4AF37;
    border: 2px solid #D4AF37;
}

.btn-outline:hover {
    background: #D4AF37;
    color: #1a1a1a;
    transform: translateY(-3px);
}

.hero-features {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #999;
    font-size: 0.9rem;
}

.feature-item i {
    color: #D4AF37;
    font-size: 1.1rem;
}

.hero-visual {
    flex: 1;
    position: relative;
}

.hero-image-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
    overflow: visible;
}

.image-frame {
    position: relative;
    border-radius: 30px;
    overflow: hidden;
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.3),
        0 15px 30px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: perspective(1200px) rotateY(-8deg) rotateX(3deg);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 3px solid rgba(212, 175, 55, 0.2);
}

.image-frame::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, #D4AF37, transparent, #D4AF37);
    border-radius: 35px;
    z-index: -1;
    opacity: 0.3;
    filter: blur(10px);
}

.image-frame:hover {
    transform: perspective(1200px) rotateY(-2deg) rotateX(1deg) scale(1.02);
    box-shadow:
        0 40px 80px rgba(0, 0, 0, 0.4),
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.hero-main-image {
    width: 100%;
    height: auto;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.3), transparent);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-frame:hover .image-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(212, 175, 55, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.play-button:hover {
    transform: scale(1.1);
    background: rgba(212, 175, 55, 1);
}

.floating-cards {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(25px);
    border-radius: 20px;
    padding: 1.2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    animation: cardFloat 4s ease-in-out infinite;
    border: 2px solid rgba(212, 175, 55, 0.3);
    transform-origin: center;
    transition: all 0.3s ease;
}

.floating-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #D4AF37, #F4D03F, #D4AF37);
    border-radius: 22px;
    z-index: -1;
    opacity: 0.6;
    filter: blur(8px);
}

.card-1 {
    top: -8%;
    right: -28%;
    animation-delay: 0s;
    transform: rotate(-12deg) scale(0.85);
    transform-origin: left center;
}

.card-1::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -15px;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.6), transparent);
    transform: translateY(-50%) rotate(-12deg);
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.4);
}

.card-2 {
    bottom: 10%;
    left: -32%;
    animation-delay: 1.5s;
    transform: rotate(15deg) scale(0.8);
    transform-origin: right center;
}

.card-2::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -15px;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.6), transparent);
    transform: translateY(-50%) rotate(15deg);
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.4);
}

.card-3 {
    top: 35%;
    right: -38%;
    animation-delay: 3s;
    transform: rotate(-18deg) scale(0.75);
    transform-origin: left center;
}

.card-3::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -15px;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.6), transparent);
    transform: translateY(-50%) rotate(-18deg);
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.4);
}

@keyframes cardFloat {
    0%, 100% {
        transform: translateY(0px) scale(1) rotateZ(0deg);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 8px 16px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(212, 175, 55, 0.1);
    }
    25% {
        transform: translateY(-8px) scale(1.01) rotateZ(1deg);
    }
    50% {
        transform: translateY(-15px) scale(1.02) rotateZ(0deg);
        box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.2),
            0 15px 30px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(212, 175, 55, 0.2),
            0 0 20px rgba(212, 175, 55, 0.1);
    }
    75% {
        transform: translateY(-8px) scale(1.01) rotateZ(-1deg);
    }
}

.card-icon {
    font-size: 1.8rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #D4AF37, #F4D03F);
    border-radius: 15px;
    box-shadow:
        0 8px 16px rgba(212, 175, 55, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
    position: relative;
}

.card-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #D4AF37, #F4D03F);
    border-radius: 17px;
    z-index: -1;
    filter: blur(4px);
    opacity: 0.7;
}

.card-text {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.card-title {
    font-size: 1rem;
    font-weight: 700;
    color: #1a1a1a;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.card-subtitle {
    font-size: 0.85rem;
    color: #555;
    font-weight: 500;
}

.hero-scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: #D4AF37;
    cursor: pointer;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border: 2px solid #D4AF37;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.scroll-text {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
}

/* About Section */
.about {
    padding: 80px 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h3 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 1rem;
}

.about-text p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.features {
    display: grid;
    gap: 1.5rem;
}

.feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.feature i {
    font-size: 2rem;
    color: #D4AF37;
    margin-top: 0.5rem;
}

.feature h4 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.feature p {
    color: #666;
    font-size: 1rem;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Menu Section */
.menu {
    padding: 80px 0;
    background: #f8f8f8;
}

.menu-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.tab-btn {
    padding: 12px 30px;
    border: 2px solid #D4AF37;
    background: transparent;
    color: #D4AF37;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: #D4AF37;
    color: white;
}

.menu-content {
    display: none;
}

.menu-content.active {
    display: block;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.menu-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.menu-item:hover {
    transform: translateY(-5px);
}

.menu-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.menu-item h3 {
    font-size: 1.3rem;
    color: #333;
    margin: 1rem;
}

.menu-item p {
    color: #666;
    margin: 0 1rem;
    font-size: 0.95rem;
}

.menu-item .price {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: #D4AF37;
    margin: 1rem;
    text-align: left;
}

/* Gallery Section */
.gallery {
    padding: 80px 0;
    background: white;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.gallery-item {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: #f8f8f8;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-item i {
    font-size: 1.5rem;
    color: #D4AF37;
    margin-top: 0.5rem;
}

.contact-item h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: #666;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 12px;
    margin-bottom: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #D4AF37;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 2rem 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-logo img {
    width: 30px;
    height: 30px;
}

.footer-logo span {
    font-size: 1.5rem;
    font-weight: 700;
    color: #D4AF37;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: white;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #D4AF37;
}

.footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #555;
}

/* Mobile Navigation Styles */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(0, 0, 0, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-container {
        flex-direction: column;
        text-align: center;
        padding: 120px 20px 50px;
        gap: 2rem;
    }

    .hero-content {
        max-width: 100%;
    }

    .title-line-1 {
        font-size: 1.4rem;
    }

    .title-line-2 {
        font-size: 3rem;
    }

    .title-accent {
        font-size: 1.5rem;
        letter-spacing: 4px;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .hero-features {
        justify-content: center;
        gap: 1rem;
    }

    .floating-cards {
        display: none;
    }

    .image-frame {
        transform: none;
        border-radius: 20px;
    }

    .hero-scroll-indicator {
        position: relative;
        margin-top: 2rem;
        animation: none;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .menu-grid {
        grid-template-columns: 1fr;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .menu-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
    }
}

/* Additional animations and effects */
.menu-item,
.gallery-item,
.feature,
.contact-item {
    transition: all 0.3s ease;
}

/* Scroll animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    animation: fadeInUp 0.6s ease forwards;
}

/* Loading spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #D4AF37;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hover effects for better UX */
.nav-menu a,
.btn,
.social-links a {
    position: relative;
    overflow: hidden;
}

.nav-menu a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: #D4AF37;
    transition: width 0.3s ease;
}

.nav-menu a:hover::before {
    width: 100%;
}

/* Improved form styling */
.contact-form input:focus,
.contact-form textarea:focus {
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

/* Better button hover effects */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}
